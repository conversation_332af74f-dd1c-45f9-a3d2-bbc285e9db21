import billingService from './billing.js'
import { ElMessage } from 'element-plus'

class OllamaService {
  constructor() {
    this.baseURL = 'http://localhost:11434'
    this.config = {
      baseURL: this.baseURL,
      timeout: 30000
    }
    this.loadUserConfig()
  }

  // 加载用户配置
  loadUserConfig() {
    try {
      const saved = localStorage.getItem('ollamaConfig')
      if (saved) {
        const userConfig = JSON.parse(saved)
        this.config = { ...this.config, ...userConfig }
        this.baseURL = this.config.baseURL || 'http://localhost:11434'
      }
    } catch (error) {
      console.error('加载Ollama配置失败:', error)
    }
  }

  // 更新配置
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
    this.baseURL = this.config.baseURL || 'http://localhost:11434'
    try {
      localStorage.setItem('ollamaConfig', JSON.stringify(this.config))
    } catch (error) {
      console.error('保存Ollama配置失败:', error)
    }
  }

  // 获取配置
  getConfig() {
    return this.config
  }

  // 构建请求URL
  buildURL(endpoint) {
    return `${this.baseURL}${endpoint}`
  }

  // 构建请求头
  buildHeaders() {
    return {
      'Content-Type': 'application/json'
    }
  }

  // 检查Ollama服务是否可用
  async checkService() {
    try {
      const response = await fetch(this.buildURL('/api/tags'), {
        method: 'GET',
        headers: this.buildHeaders(),
        signal: AbortSignal.timeout(5000)
      })
      return response.ok
    } catch (error) {
      console.error('Ollama服务检查失败:', error)
      return false
    }
  }

  // 获取可用模型列表
  async getAvailableModels() {
    try {
      const response = await fetch(this.buildURL('/api/tags'), {
        method: 'GET',
        headers: this.buildHeaders(),
        signal: AbortSignal.timeout(10000)
      })

      if (!response.ok) {
        throw new Error(`获取模型列表失败: ${response.status}`)
      }

      const data = await response.json()
      return data.models || []
    } catch (error) {
      console.error('获取Ollama模型列表失败:', error)
      throw error
    }
  }

  // 生成文本内容（非流式）
  async generateText(prompt, options = {}) {
    const model = options.model || 'llama2'
    
    // 估算输入token数量
    const estimatedInputTokens = billingService.estimateTokens(prompt)

    const requestBody = {
      model: model,
      prompt: prompt,
      stream: false,
      options: {
        temperature: options.temperature || 0.7,
        top_p: options.topP || 0.9,
        top_k: options.topK || 40,
        num_predict: options.maxTokens || -1
      }
    }

    try {
      const response = await fetch(this.buildURL('/api/generate'), {
        method: 'POST',
        headers: this.buildHeaders(),
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(this.config.timeout || 30000)
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Ollama API请求失败: ${response.status} - ${errorText}`)
      }

      const data = await response.json()
      const content = data.response || ''

      // 记录token使用情况
      const outputTokens = billingService.estimateTokens(content)
      billingService.recordAPICall({
        type: options.type || 'generation',
        model: model,
        content: prompt,
        response: content,
        inputTokens: estimatedInputTokens,
        outputTokens: outputTokens,
        status: 'success'
      })

      return content
    } catch (error) {
      // 记录失败的API调用
      billingService.recordAPICall({
        type: options.type || 'generation',
        model: model,
        content: prompt,
        response: '',
        inputTokens: estimatedInputTokens,
        outputTokens: 0,
        status: 'failed'
      })
      throw error
    }
  }

  // 流式生成文本内容
  async generateTextStream(prompt, options = {}, onChunk = null) {
    console.log('开始Ollama流式生成，prompt:', prompt.substring(0, 100) + '...')
    
    const model = options.model || 'llama2'
    
    // 验证prompt参数
    if (!prompt || typeof prompt !== 'string') {
      throw new Error('无效的prompt参数')
    }

    // 估算输入token数量
    const estimatedInputTokens = billingService.estimateTokens(prompt)

    const requestBody = {
      model: model,
      prompt: prompt,
      stream: true,
      options: {
        temperature: options.temperature || 0.7,
        top_p: options.topP || 0.9,
        top_k: options.topK || 40,
        num_predict: options.maxTokens || -1
      }
    }

    console.log('Ollama请求体:', requestBody)

    let fullContent = ''
    let hasError = false

    try {
      const response = await fetch(this.buildURL('/api/generate'), {
        method: 'POST',
        headers: this.buildHeaders(),
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(300000) // 5分钟超时
      })

      console.log('Ollama API响应状态:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        hasError = true
        console.error('Ollama API错误响应:', errorText)
        throw new Error(`Ollama API请求失败: ${response.status} - ${errorText}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      let streamFinished = false
      let processedChunks = 0

      try {
        while (!streamFinished) {
          const { done, value } = await reader.read()
          
          if (done) {
            console.log('Ollama读取完成，处理了', processedChunks, '个chunks，总内容长度:', fullContent.length)
            break
          }

          const chunk = decoder.decode(value, { stream: true })
          console.log('接收到Ollama原始chunk:', chunk.length, '字节')
          
          // 按行分割处理
          const lines = chunk.split('\n')
          
          for (const line of lines) {
            const trimmedLine = line.trim()
            
            if (trimmedLine) {
              try {
                const parsed = JSON.parse(trimmedLine)
                const content = parsed.response || ''
                
                if (content) {
                  fullContent += content
                  processedChunks++
                  console.log('接收到Ollama内容片段:', content.length, '字符，总长度:', fullContent.length)
                  
                  if (onChunk) {
                    try {
                      onChunk(content, fullContent)
                    } catch (chunkError) {
                      console.error('onChunk回调错误:', chunkError)
                    }
                  }
                }
                
                // 检查是否完成
                if (parsed.done === true) {
                  console.log('Ollama生成完成标记')
                  streamFinished = true
                  break
                }
                
              } catch (e) {
                console.log('解析Ollama数据失败，原始数据:', trimmedLine, '错误:', e.message)
                // 继续处理其他数据，不中断流式处理
              }
            }
          }
        }
        
        console.log('Ollama流式生成最终完成，总处理chunks:', processedChunks, '最终内容长度:', fullContent.length)
        
      } catch (streamError) {
        console.error('Ollama流式读取错误:', streamError)
        
        // 如果是网络错误或超时，但已经有部分内容，可以考虑返回部分内容
        if (fullContent.length > 0 && (
          streamError.name === 'AbortError' || 
          streamError.message.includes('timeout') ||
          streamError.message.includes('network')
        )) {
          console.log('网络问题导致Ollama流式中断，但已获得部分内容:', fullContent.length, '字符')
          ElMessage.warning('网络不稳定，已获得部分生成内容')
          // 不抛出错误，返回已获得的内容
        } else {
          hasError = true
          throw streamError
        }
      } finally {
        try {
          reader.releaseLock()
        } catch (e) {
          console.log('释放Ollama reader锁失败:', e.message)
        }
      }

      // 流式生成成功，记录token使用
      const outputTokens = billingService.estimateTokens(fullContent)
      billingService.recordAPICall({
        type: options.type || 'generation',
        model: model,
        content: prompt,
        response: fullContent,
        inputTokens: estimatedInputTokens,
        outputTokens: outputTokens,
        status: 'success'
      })

      return fullContent
    } catch (error) {
      console.error('Ollama流式生成错误:', error)
      // 只有在发生错误时才记录失败调用
      if (hasError) {
        billingService.recordAPICall({
          type: options.type || 'generation',
          model: model,
          content: prompt,
          response: fullContent,
          inputTokens: estimatedInputTokens,
          outputTokens: billingService.estimateTokens(fullContent),
          status: 'failed'
        })
      }
      throw error
    }
  }

  // 验证模型是否可用
  async validateModel(modelName) {
    try {
      const models = await this.getAvailableModels()
      return models.some(model => model.name === modelName)
    } catch (error) {
      console.error('验证Ollama模型失败:', error)
      return false
    }
  }

  // 拉取模型
  async pullModel(modelName, onProgress = null) {
    try {
      const response = await fetch(this.buildURL('/api/pull'), {
        method: 'POST',
        headers: this.buildHeaders(),
        body: JSON.stringify({ name: modelName, stream: true })
      })

      if (!response.ok) {
        throw new Error(`拉取模型失败: ${response.status}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line)
              if (onProgress) {
                onProgress(data)
              }
            } catch (e) {
              console.log('解析拉取进度失败:', e.message)
            }
          }
        }
      }

      return true
    } catch (error) {
      console.error('拉取Ollama模型失败:', error)
      throw error
    }
  }
}

export default new OllamaService()
