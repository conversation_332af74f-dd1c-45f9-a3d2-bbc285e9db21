<template>
  <div class="ollama-manager">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>🦙 Ollama 模型管理</h3>
          <div class="header-actions">
            <el-button @click="checkOllamaService" :loading="checking">
              <el-icon><Connection /></el-icon>
              检查服务
            </el-button>
            <el-button @click="refreshModels" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新列表
            </el-button>
          </div>
        </div>
      </template>

      <!-- 服务状态 -->
      <div class="service-status">
        <el-alert
          :title="serviceStatus.title"
          :type="serviceStatus.type"
          :description="serviceStatus.description"
          show-icon
          :closable="false"
        />
      </div>

      <!-- Ollama配置 -->
      <div class="ollama-config">
        <h4>🔧 服务配置</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="Ollama地址">
              <el-input 
                v-model="ollamaConfig.baseURL" 
                placeholder="http://localhost:11434"
                @change="updateOllamaConfig"
              >
                <template #prepend>HTTP://</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="超时时间(秒)">
              <el-input-number 
                v-model="ollamaConfig.timeout" 
                :min="5" 
                :max="300"
                @change="updateOllamaConfig"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 已安装模型列表 -->
      <div class="installed-models">
        <h4>📦 已安装模型</h4>
        <el-table 
          :data="installedModels" 
          v-loading="loading"
          empty-text="暂无已安装的模型"
        >
          <el-table-column prop="name" label="模型名称" min-width="200">
            <template #default="{ row }">
              <div class="model-name">
                <el-tag type="success" size="small">{{ row.name }}</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="size" label="大小" width="120">
            <template #default="{ row }">
              <span>{{ formatSize(row.size) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="modified_at" label="修改时间" width="180">
            <template #default="{ row }">
              <span>{{ formatDate(row.modified_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button 
                size="small" 
                @click="testModel(row.name)"
                :loading="testingModel === row.name"
              >
                测试
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                @click="deleteModel(row.name)"
                :loading="deletingModel === row.name"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 推荐模型 -->
      <div class="recommended-models">
        <h4>🌟 推荐模型</h4>
        <div class="model-grid">
          <div 
            v-for="model in recommendedModels"
            :key="model.name"
            class="model-card"
            :class="{ 'installed': isModelInstalled(model.name) }"
          >
            <div class="model-info">
              <div class="model-header">
                <h5>{{ model.name }}</h5>
                <el-tag 
                  :type="model.type === 'large' ? 'warning' : 'info'" 
                  size="small"
                >
                  {{ model.size }}
                </el-tag>
              </div>
              <p>{{ model.description }}</p>
              <div class="model-tags">
                <el-tag 
                  v-for="tag in model.tags"
                  :key="tag"
                  size="small"
                  type="info"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
            <div class="model-actions">
              <el-button 
                v-if="!isModelInstalled(model.name)"
                type="primary" 
                size="small"
                @click="pullModel(model.name)"
                :loading="pullingModel === model.name"
              >
                <el-icon><Download /></el-icon>
                安装
              </el-button>
              <el-tag v-else type="success" size="small">
                <el-icon><Check /></el-icon>
                已安装
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 自定义拉取 -->
      <div class="custom-pull">
        <h4>📥 自定义拉取</h4>
        <el-row :gutter="20">
          <el-col :span="16">
            <el-input 
              v-model="customModelName" 
              placeholder="输入模型名称，如：llama2, mistral, codellama"
            />
          </el-col>
          <el-col :span="8">
            <el-button 
              type="primary" 
              @click="pullCustomModel"
              :loading="pullingModel === customModelName"
              :disabled="!customModelName"
            >
              <el-icon><Download /></el-icon>
              拉取模型
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 拉取进度对话框 -->
    <el-dialog 
      v-model="showPullProgress" 
      title="模型拉取进度" 
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="pull-progress">
        <el-progress 
          :percentage="pullProgress.percentage" 
          :status="pullProgress.status"
        />
        <p class="progress-text">{{ pullProgress.text }}</p>
        <div class="progress-details">
          <p>模型: {{ pullProgress.model }}</p>
          <p>状态: {{ pullProgress.statusText }}</p>
          <p v-if="pullProgress.total">
            进度: {{ formatSize(pullProgress.completed) }} / {{ formatSize(pullProgress.total) }}
          </p>
        </div>
      </div>
      <template #footer>
        <el-button @click="cancelPull" :disabled="pullProgress.status === 'success'">
          {{ pullProgress.status === 'success' ? '完成' : '取消' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Connection, Refresh, Download, Check } from '@element-plus/icons-vue'
import ollamaService from '../services/ollama.js'

// 响应式数据
const loading = ref(false)
const checking = ref(false)
const installedModels = ref([])
const testingModel = ref('')
const deletingModel = ref('')
const pullingModel = ref('')
const customModelName = ref('')
const showPullProgress = ref(false)

// Ollama配置
const ollamaConfig = reactive({
  baseURL: 'http://localhost:11434',
  timeout: 30
})

// 服务状态
const serviceStatus = reactive({
  title: '服务状态检查中...',
  type: 'info',
  description: '正在检查Ollama服务是否可用'
})

// 拉取进度
const pullProgress = reactive({
  percentage: 0,
  status: '',
  text: '',
  model: '',
  statusText: '',
  completed: 0,
  total: 0
})

// 推荐模型
const recommendedModels = ref([
  {
    name: 'llama2',
    size: '3.8GB',
    type: 'medium',
    description: 'Meta的Llama2模型，平衡性能和资源消耗',
    tags: ['通用', '中文', '推荐']
  },
  {
    name: 'llama3',
    size: '4.7GB', 
    type: 'large',
    description: 'Meta的最新Llama3模型，性能更强',
    tags: ['最新', '强大', '推荐']
  },
  {
    name: 'mistral',
    size: '4.1GB',
    type: 'medium',
    description: 'Mistral AI的高效模型，速度快',
    tags: ['高效', '快速', '轻量']
  },
  {
    name: 'codellama',
    size: '3.8GB',
    type: 'medium', 
    description: '专门用于代码生成的Llama模型',
    tags: ['代码', '编程', '专业']
  },
  {
    name: 'llama2-chinese',
    size: '3.8GB',
    type: 'medium',
    description: '中文优化的Llama2模型',
    tags: ['中文', '本土', '优化']
  }
])

// 方法
const checkOllamaService = async () => {
  checking.value = true
  try {
    const isAvailable = await ollamaService.checkService()
    if (isAvailable) {
      serviceStatus.title = 'Ollama服务正常'
      serviceStatus.type = 'success'
      serviceStatus.description = 'Ollama服务运行正常，可以使用本地模型'
      await refreshModels()
    } else {
      serviceStatus.title = 'Ollama服务不可用'
      serviceStatus.type = 'error'
      serviceStatus.description = '请确保Ollama已安装并运行在 ' + ollamaConfig.baseURL
    }
  } catch (error) {
    serviceStatus.title = 'Ollama服务检查失败'
    serviceStatus.type = 'error'
    serviceStatus.description = error.message
  } finally {
    checking.value = false
  }
}

const refreshModels = async () => {
  loading.value = true
  try {
    const models = await ollamaService.getAvailableModels()
    installedModels.value = models
    ElMessage.success(`已加载 ${models.length} 个模型`)
  } catch (error) {
    ElMessage.error('获取模型列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const updateOllamaConfig = () => {
  ollamaService.updateConfig(ollamaConfig)
  ElMessage.success('Ollama配置已更新')
}

const isModelInstalled = (modelName) => {
  return installedModels.value.some(model => model.name === modelName)
}

const testModel = async (modelName) => {
  testingModel.value = modelName
  try {
    const result = await ollamaService.generateText('你好，请简单介绍一下自己。', {
      model: modelName,
      maxTokens: 100
    })
    ElMessage.success('模型测试成功')
    ElMessageBox.alert(result, `${modelName} 测试结果`, {
      confirmButtonText: '确定'
    })
  } catch (error) {
    ElMessage.error('模型测试失败: ' + error.message)
  } finally {
    testingModel.value = ''
  }
}

const pullModel = async (modelName) => {
  pullingModel.value = modelName
  showPullProgress.value = true
  
  pullProgress.model = modelName
  pullProgress.percentage = 0
  pullProgress.status = ''
  pullProgress.text = '开始拉取模型...'
  pullProgress.statusText = '准备中'
  
  try {
    await ollamaService.pullModel(modelName, (progress) => {
      pullProgress.statusText = progress.status || '下载中'
      pullProgress.text = progress.status || '正在拉取模型...'
      
      if (progress.completed && progress.total) {
        pullProgress.completed = progress.completed
        pullProgress.total = progress.total
        pullProgress.percentage = Math.round((progress.completed / progress.total) * 100)
      }
    })
    
    pullProgress.percentage = 100
    pullProgress.status = 'success'
    pullProgress.text = '模型拉取完成！'
    pullProgress.statusText = '完成'
    
    ElMessage.success(`模型 ${modelName} 拉取成功`)
    await refreshModels()
  } catch (error) {
    pullProgress.status = 'exception'
    pullProgress.text = '模型拉取失败: ' + error.message
    ElMessage.error('模型拉取失败: ' + error.message)
  } finally {
    pullingModel.value = ''
  }
}

const pullCustomModel = () => {
  if (customModelName.value) {
    pullModel(customModelName.value)
    customModelName.value = ''
  }
}

const deleteModel = async (modelName) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模型 "${modelName}" 吗？此操作不可恢复。`,
      '删除模型',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    deletingModel.value = modelName
    // 这里需要实现删除模型的API调用
    // await ollamaService.deleteModel(modelName)
    ElMessage.success(`模型 ${modelName} 删除成功`)
    await refreshModels()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除模型失败: ' + error.message)
    }
  } finally {
    deletingModel.value = ''
  }
}

const cancelPull = () => {
  showPullProgress.value = false
  pullingModel.value = ''
}

const formatSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  // 加载Ollama配置
  const config = ollamaService.getConfig()
  Object.assign(ollamaConfig, config)
  
  // 检查服务状态
  checkOllamaService()
})
</script>

<style scoped>
.ollama-manager {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.service-status {
  margin-bottom: 20px;
}

.ollama-config {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.ollama-config h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.installed-models {
  margin-bottom: 30px;
}

.installed-models h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.model-name {
  display: flex;
  align-items: center;
  gap: 10px;
}

.recommended-models h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.model-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
}

.model-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.3s;
}

.model-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.model-card.installed {
  background-color: #f0f9ff;
  border-color: #67c23a;
}

.model-info {
  flex: 1;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.model-header h5 {
  margin: 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.model-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
}

.model-tags {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.model-actions {
  margin-left: 15px;
}

.custom-pull {
  margin-bottom: 20px;
}

.custom-pull h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.pull-progress {
  text-align: center;
}

.progress-text {
  margin: 15px 0;
  color: #606266;
  font-size: 14px;
}

.progress-details {
  text-align: left;
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
}

.progress-details p {
  margin: 5px 0;
  color: #606266;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .model-grid {
    grid-template-columns: 1fr;
  }
  
  .model-card {
    flex-direction: column;
    gap: 15px;
  }
  
  .model-actions {
    margin-left: 0;
    align-self: stretch;
  }
}
</style>
